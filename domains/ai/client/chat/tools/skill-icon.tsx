import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';

interface Props {
  skillsets: SkillsetSelectDTO[];
  toolInvocation: ToolInvocation;
}

/**
 *  通过 Skillsets，来真正获得技能图标
 */
function SkillIconBase(props: Props): React.ReactNode {
  // 特殊 UI 配置
  const skillsetUIMap = React.useMemo(
    () => AISkillsetClientRegistry.getManySkillsetUI(props.skillsets),
    [props.skillsets],
  );
  let nodeIcon;
  const skillUICfg = skillsetUIMap ? skillsetUIMap[props.toolInvocation.toolName] : undefined;

  if (skillUICfg?.logo) {
    // 从服务端 VO 拿到图标，转换为 INodeIconValue
    nodeIcon = {
      kind: 'avatar' as const,
      avatar: skillUICfg.logo,
      name: skillUICfg.name,
    };
  }

  // 客户端自定义置了 skill icon (不是 skillsets 哦)，覆盖这个图标
  if (skillUICfg?.customIcon) {
    nodeIcon = {
      kind: 'avatar' as const,
      avatar: skillUICfg.customIcon,
    };
  }

  // Use a simple fallback icon if nodeIcon is undefined
  if (!nodeIcon) {
    return null;
  }

  return (
    No
  )
  <div>Icon placeholder</div>;
}

export const SkillIcon = React.memo(SkillIconBase);
